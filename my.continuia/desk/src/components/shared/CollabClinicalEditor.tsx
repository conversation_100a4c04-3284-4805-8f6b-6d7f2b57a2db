import React, { useEffect, useState, useCallback } from 'react';
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Collaboration from '@tiptap/extension-collaboration';
import CollaborationCursor from '@tiptap/extension-collaboration-cursor';
import Blockquote from '@tiptap/extension-blockquote';
import BulletList from '@tiptap/extension-bullet-list';
import OrderedList from '@tiptap/extension-ordered-list';
import ListItem from '@tiptap/extension-list-item';
import Table from '@tiptap/extension-table';
import TableRow from '@tiptap/extension-table-row';
import TableCell from '@tiptap/extension-table-cell';
import TableHeader from '@tiptap/extension-table-header';
import DropCursor from '@tiptap/extension-dropcursor';
import GapCursor from '@tiptap/extension-gapcursor';
import * as Y from 'yjs';
import { WebsocketProvider } from 'y-websocket';
import { IndexeddbPersistence } from 'y-indexeddb';
import { useAuth } from '../../hooks/useAuth';
import { SnomedExtension } from './extensions/SnomedExtension';
import { RxNavExtension } from './extensions/RxNavExtension';
import { MedicalBlockExtension } from './extensions/MedicalBlockExtension';
import { CollaborationToolbar } from './CollaborationToolbar';
import { UserPresence } from './UserPresence';
import { LoadingSpinner } from '../ui/LoadingSpinner';
import { AlertCircle, Users, Wifi, WifiOff } from 'lucide-react';

export interface CollabClinicalEditorProps {
  caseId: string;
  noteType: string;
  className?: string;
  placeholder?: string;
  disabled?: boolean;
  height?: number;
  onSave?: (content: string) => void;
  onError?: (error: string) => void;
}

interface CollaboratorInfo {
  userId: string;
  userName: string;
  userColor: string;
  cursorPosition?: any;
}

export const CollabClinicalEditor: React.FC<CollabClinicalEditorProps> = ({
  caseId,
  noteType,
  className = '',
  placeholder = 'Start typing your clinical notes...',
  disabled = false,
  height = 400,
  onSave,
  onError,
}) => {
  const { user } = useAuth();
  const [isConnected, setIsConnected] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [collaborators, setCollaborators] = useState<CollaboratorInfo[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [ydoc, setYdoc] = useState<Y.Doc | null>(null);
  const [provider, setProvider] = useState<WebsocketProvider | null>(null);

  // Generate user color based on user ID
  const getUserColor = useCallback((userId: string) => {
    const colors = [
      '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
      '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
    ];
    const hash = userId.split('').reduce((a, b) => {
      a = ((a << 5) - a) + b.charCodeAt(0);
      return a & a;
    }, 0);
    return colors[Math.abs(hash) % colors.length];
  }, []);

  // Initialize yJS document and providers
  useEffect(() => {
    if (!caseId || !noteType || !user) return;

    const doc = new Y.Doc();
    const documentId = `note:${caseId}:${noteType}`;
    
    // WebSocket provider for real-time collaboration
    const baseWsUrl = import.meta.env.VITE_WS_URL || 'ws://localhost:3001/ws';

    // Construct WebSocket URL with query parameters
    const wsUrl = new URL(baseWsUrl);
    wsUrl.searchParams.set('caseId', caseId);
    wsUrl.searchParams.set('noteType', noteType);
    wsUrl.searchParams.set('token', localStorage.getItem('auth_token') || '');

    // y-websocket expects base URL without document ID, it will append the document ID
    const wsProvider = new WebsocketProvider(
      wsUrl.toString(),
      documentId,
      doc
    );

    // IndexedDB provider for offline persistence
    const indexeddbProvider = new IndexeddbPersistence(documentId, doc);

    // Connection status handlers
    wsProvider.on('status', (event: { status: string }) => {
      setIsConnected(event.status === 'connected');
      if (event.status === 'connected') {
        setError(null);
      }
    });

    wsProvider.on('connection-error', (error: any) => {
      setError('Connection failed. Working offline.');
      onError?.('Connection failed. Working offline.');
    });

    // Awareness (user presence) handlers
    wsProvider.awareness.on('change', () => {
      const states = Array.from(wsProvider.awareness.getStates().entries());
      const activeCollaborators: CollaboratorInfo[] = states
        .filter(([clientId, state]) => state.user && clientId !== wsProvider.awareness.clientID)
        .map(([clientId, state]) => ({
          userId: state.user.id,
          userName: state.user.name,
          userColor: state.user.color,
          cursorPosition: state.cursor
        }));
      
      setCollaborators(activeCollaborators);
    });

    // Set user info for awareness
    wsProvider.awareness.setLocalStateField('user', {
      id: user.id,
      name: user.firstName ? `${user.firstName} ${user.lastName}` : user.email,
      color: getUserColor(user.id)
    });

    setYdoc(doc);
    setProvider(wsProvider);
    setIsLoading(false);

    return () => {
      wsProvider.destroy();
      indexeddbProvider.destroy();
      doc.destroy();
    };
  }, [caseId, noteType, user, getUserColor, onError]);

  // Initialize Tiptap editor only when yJS document is ready
  const editor = useEditor({
    extensions: ydoc && provider ? [
      StarterKit.configure({
        // Disable built-in extensions that we want to configure separately
        blockquote: false,
        bulletList: false,
        orderedList: false,
        listItem: false,
        dropcursor: false,
        gapcursor: false,
      }),
      Collaboration.configure({
        document: ydoc,
        field: 'default', // Specify the fragment name
      }),
      CollaborationCursor.configure({
        provider: provider,
        user: user ? {
          name: user.firstName ? `${user.firstName} ${user.lastName}` : user.email,
          color: getUserColor(user.id),
        } : undefined,
      }),
      // Re-add the extensions we disabled from StarterKit with custom configuration
      Blockquote,
      BulletList,
      OrderedList,
      ListItem,
      DropCursor,
      GapCursor,
      // Table extensions
      Table.configure({
        resizable: true,
      }),
      TableRow,
      TableHeader,
      TableCell,
      // Medical extensions
      SnomedExtension,
      RxNavExtension,
      MedicalBlockExtension,
    ] : [
      StarterKit, // Basic editor without collaboration
    ],
    editorProps: {
      attributes: {
        class: `prose prose-sm sm:prose lg:prose-lg xl:prose-2xl mx-auto focus:outline-none ${disabled ? 'opacity-60 pointer-events-none' : ''}`,
        style: `min-height: ${height}px;`,
      },
    },
    editable: !disabled && ydoc !== null,
    content: '', // Content will be loaded from yJS document
  }, [ydoc, provider, user, disabled, height, getUserColor]);

  // Auto-save functionality
  useEffect(() => {
    if (!editor || !onSave) return;

    const saveContent = () => {
      const content = editor.getHTML();
      onSave(content);
    };

    // Save on content change with debounce
    const timeoutId = setTimeout(saveContent, 2000);
    
    return () => clearTimeout(timeoutId);
  }, [editor?.getHTML(), onSave]);

  // Handle loading state
  if (isLoading) {
    return (
      <div className={`flex items-center justify-center p-8 ${className}`}>
        <LoadingSpinner />
        <span className="ml-2 text-gray-600">Initializing collaborative editor...</span>
      </div>
    );
  }

  // Handle error state
  if (error && !editor) {
    return (
      <div className={`flex items-center justify-center p-8 text-red-600 ${className}`}>
        <AlertCircle className="h-5 w-5 mr-2" />
        <span>{error}</span>
      </div>
    );
  }

  return (
    <div className={`collab-clinical-editor ${className}`}>
      {/* Header with collaboration info */}
      <div className="flex items-center justify-between mb-4 p-3 bg-gray-50 rounded-lg">
        <div className="flex items-center space-x-3">
          <div className="flex items-center space-x-2">
            {isConnected ? (
              <Wifi className="h-4 w-4 text-green-500" />
            ) : (
              <WifiOff className="h-4 w-4 text-red-500" />
            )}
            <span className="text-sm text-gray-600">
              {isConnected ? 'Connected' : 'Offline'}
            </span>
          </div>
          
          {collaborators.length > 0 && (
            <div className="flex items-center space-x-2">
              <Users className="h-4 w-4 text-gray-500" />
              <span className="text-sm text-gray-600">
                {collaborators.length} collaborator{collaborators.length > 1 ? 's' : ''}
              </span>
            </div>
          )}
        </div>

        <UserPresence collaborators={collaborators} />
      </div>

      {/* Collaboration toolbar */}
      {editor && (
        <CollaborationToolbar 
          editor={editor} 
          disabled={disabled}
          className="mb-4"
        />
      )}

      {/* Error banner */}
      {error && (
        <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
          <div className="flex items-center">
            <AlertCircle className="h-4 w-4 text-yellow-600 mr-2" />
            <span className="text-sm text-yellow-800">{error}</span>
          </div>
        </div>
      )}

      {/* Editor content */}
      <div className="border border-gray-300 rounded-lg overflow-hidden">
        <EditorContent 
          editor={editor}
          className="min-h-[400px] p-4 focus-within:ring-2 focus-within:ring-blue-500"
          placeholder={placeholder}
        />
      </div>

      {/* Footer with save status */}
      <div className="mt-2 flex justify-between items-center text-xs text-gray-500">
        <div>
          {isConnected ? 'Auto-saving...' : 'Changes saved locally'}
        </div>
        <div>
          {collaborators.length > 0 && (
            <span>
              Editing with {collaborators.map(c => c.userName).join(', ')}
            </span>
          )}
        </div>
      </div>
    </div>
  );
};
