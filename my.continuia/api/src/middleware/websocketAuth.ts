import jwt from 'jsonwebtoken';
import { parse as parseCookie } from 'cookie';
import { db } from '../db/index';
import { users } from '../db/schema/users';
import { eq } from 'drizzle-orm';
import { logger } from '../utils/structuredLogger';
import { PolicyEngine, type PolicyContext } from '../services/policyEngine.js';
import { securityEventLogger } from './requestMonitoring.js';

// Reuse the same JWT payload interface from opalAuth
interface JWTPayload {
  userId: string;
  email: string;
  roles: string[];
  sessionId: string;
  iat: number;
  exp: number;
}

// WebSocket verification info interface matching ws library expectations
interface WebSocketVerificationInfo {
  origin: string;
  secure: boolean;
  req: {
    url: string;
    headers: Record<string, string | string[] | undefined>;
    userId?: string;
    caseId?: string;
    noteType?: string;
    userToken?: string;
    user?: {
      id: string;
      email: string;
      firstName: string;
      lastName: string;
      isActive: boolean;
      roles: string[];
      sessionId: string;
    };
  };
}

/**
 * WebSocket Authentication Middleware
 * Handles authentication for WebSocket connections using tokens from headers or cookies
 */
export class WebSocketAuthMiddleware {
  /**
   * Extract authentication token from WebSocket request
   * Supports both Authorization header (Bearer token) and HTTP-only cookies
   */
  private static extractToken(req: any): string | null {
    // Try Authorization header first (Bearer token)
    const authHeader = req.headers.authorization;
    if (authHeader && typeof authHeader === 'string' && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }

    // Try custom auth header
    const customAuthHeader = req.headers['x-auth-token'];
    if (customAuthHeader && typeof customAuthHeader === 'string') {
      return customAuthHeader;
    }

    // Try HTTP-only cookie
    const cookieHeader = req.headers.cookie;
    if (cookieHeader && typeof cookieHeader === 'string') {
      try {
        const cookies = parseCookie(cookieHeader);
        if (cookies.auth_token) {
          return cookies.auth_token;
        }
      } catch (error) {
        logger.warn('Failed to parse cookies from WebSocket request', { error: error.message });
      }
    }

    return null;
  }

  /**
   * Extract connection parameters from WebSocket request
   * Supports both headers and URL parameters
   */
  private static extractConnectionParams(req: any): {
    userId?: string;
    caseId?: string;
    noteType?: string;
  } {
    // Try headers first (preferred method)
    const userId = req.headers['x-user-id'];
    const caseId = req.headers['x-case-id'];
    const noteType = req.headers['x-note-type'];

    if (userId && caseId && noteType) {
      return {
        userId: typeof userId === 'string' ? userId : userId[0],
        caseId: typeof caseId === 'string' ? caseId : caseId[0],
        noteType: typeof noteType === 'string' ? noteType : noteType[0],
      };
    }

    // Fallback to URL parsing if headers are not available
    try {
      const url = new URL(req.url, 'ws://localhost');
      const pathParts = url.pathname.split('/');
      
      // Expected format: /ws/note:caseId:noteType or /ws/caseId/noteType
      if (pathParts.length >= 4) {
        const [, , caseIdPart, noteTypePart] = pathParts;
        return {
          caseId: caseIdPart,
          noteType: noteTypePart,
        };
      }

      // Try query parameters as last resort
      return {
        userId: url.searchParams.get('userId') || undefined,
        caseId: url.searchParams.get('caseId') || undefined,
        noteType: url.searchParams.get('noteType') || undefined,
      };
    } catch (error) {
      logger.warn('Failed to parse WebSocket URL parameters', { 
        url: req.url, 
        error: error.message 
      });
      return {};
    }
  }

  /**
   * Verify JWT token and get user information
   */
  private static async verifyToken(token: string): Promise<{
    decoded: JWTPayload;
    user: any;
  } | null> {
    if (!process.env.JWT_SECRET) {
      logger.error('JWT_SECRET not configured for WebSocket authentication');
      return null;
    }

    try {
      // Verify JWT token
      const decoded = jwt.verify(token, process.env.JWT_SECRET) as JWTPayload;
      
      // Additional token validation (same as opalAuth)
      if (!decoded.userId || !decoded.sessionId) {
        securityEventLogger.logFailedAuth(
          { headers: {}, ip: 'websocket', originalUrl: '/ws' } as any,
          'INVALID_TOKEN_FORMAT'
        );
        return null;
      }

      // Get user from database
      const [user] = await db
        .select({
          id: users.id,
          email: users.email,
          firstName: users.firstName,
          lastName: users.lastName,
          isActive: users.isActive,
        })
        .from(users)
        .where(eq(users.id, decoded.userId))
        .limit(1);

      if (!user) {
        securityEventLogger.logFailedAuth(
          { headers: {}, ip: 'websocket', originalUrl: '/ws' } as any,
          'USER_NOT_FOUND',
          { userId: decoded.userId }
        );
        return null;
      }

      if (!user.isActive) {
        securityEventLogger.logFailedAuth(
          { headers: {}, ip: 'websocket', originalUrl: '/ws' } as any,
          'INACTIVE_USER',
          { userId: decoded.userId }
        );
        return null;
      }

      return { decoded, user };
    } catch (error) {
      if (error instanceof jwt.JsonWebTokenError) {
        securityEventLogger.logFailedAuth(
          { headers: {}, ip: 'websocket', originalUrl: '/ws' } as any,
          'INVALID_TOKEN',
          { error: error.message }
        );
      } else if (error instanceof jwt.TokenExpiredError) {
        securityEventLogger.logFailedAuth(
          { headers: {}, ip: 'websocket', originalUrl: '/ws' } as any,
          'EXPIRED_TOKEN'
        );
      } else {
        logger.error('Unexpected error during WebSocket token verification: ' + (error instanceof Error ? error.message : String(error)));
      }
      return null;
    }
  }

  /**
   * Main verification method for WebSocket connections
   * This method should be used in the WebSocket server's verifyClient callback
   */
  static async verifyClient(info: WebSocketVerificationInfo): Promise<boolean> {
    const { req } = info;
    
    logger.info('WebSocket authentication attempt', {
      url: req.url,
      hasAuthHeader: !!req.headers.authorization,
      hasCookieHeader: !!req.headers.cookie,
      hasCustomAuthHeader: !!req.headers['x-auth-token'],
    });

    try {
      // Check if this is a WebSocket request for our collaborative notes
      if (!req.url.startsWith('/ws')) {
        logger.warn('WebSocket connection rejected: Invalid path', { url: req.url });
        return false;
      }

      // Extract authentication token
      const token = this.extractToken(req);
      if (!token) {
        logger.warn('WebSocket connection rejected: No authentication token provided');
        return false;
      }

      // Extract connection parameters
      const params = this.extractConnectionParams(req);
      if (!params.caseId || !params.noteType) {
        logger.warn('WebSocket connection rejected: Missing required parameters', params);
        return false;
      }

      // Verify token and get user information
      const authResult = await this.verifyToken(token);
      if (!authResult) {
        logger.warn('WebSocket connection rejected: Token verification failed');
        return false;
      }

      const { decoded, user } = authResult;

      // Use userId from token if not provided in parameters
      const userId = params.userId || decoded.userId;

      // Create policy context for WebSocket connection (similar to opalAuth)
      const policyContext: PolicyContext = {
        user: {
          id: userId,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          roles: decoded.roles || ['patient'],
        },
        resource: {
          type: 'case_notes',
          id: params.caseId,
          attributes: {
            noteType: params.noteType,
          },
        },
        action: 'collaborate', // WebSocket-specific action
        environment: {
          time: new Date(),
          ip: 'websocket',
          userAgent: 'websocket-client',
        },
      };

      // Evaluate policy (same as opalAuth)
      const policyEngine = PolicyEngine.getInstance();
      const policyResult = await policyEngine.evaluate(policyContext);

      if (!policyResult.allow) {
        securityEventLogger.logAccessViolation(
          { headers: {}, ip: 'websocket', originalUrl: '/ws' } as any,
          'POLICY_DENIED',
          {
            resource: 'case_notes',
            action: 'collaborate',
            reason: policyResult.reason,
            matchedPolicies: policyResult.matchedPolicies,
          }
        );
        logger.warn('WebSocket connection rejected by policy engine', {
          userId,
          caseId: params.caseId,
          reason: policyResult.reason,
        });
        return false;
      }

      // Store authentication information in request for later use
      req.userId = userId;
      req.caseId = params.caseId;
      req.noteType = params.noteType;
      req.userToken = token;
      req.user = {
        ...user,
        roles: decoded.roles || ['patient'],
        sessionId: decoded.sessionId,
      };

      logger.info('WebSocket connection authenticated successfully', {
        userId,
        caseId: params.caseId,
        noteType: params.noteType,
        userRoles: decoded.roles,
        policyResult: policyResult.reason,
      });

      return true;
    } catch (error) {
      logger.error('WebSocket authentication error for URL ' + req.url + ': ' + (error instanceof Error ? error.message : String(error)));
      return false;
    }
  }
}
