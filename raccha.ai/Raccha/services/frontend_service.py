import json
import uuid
import logging
import os
from datetime import datetime
from typing import Dict, Any, Optional, List
from fastapi import WebSocket, WebSocketDisconnect
from fastapi.websockets import WebSocketState
import re

from .letta_api_service import LettaAPIService


class FrontendService:
    """Service class for frontend interactions - session management, WebSocket, UI logic"""
    
    def __init__(self, settings, letta_service: LettaAPIService):
        self.settings = settings
        self.letta_service = letta_service
        self.logger = logging.getLogger("frontend_service")
        
        # Session management
        self.session_data = {}
        self.session_welcome_sent = {}
        self.sessions_file = os.path.join(settings.FOLDERS_BASE_DIR, "sessions.json")
        
        # Load existing sessions
        self._load_sessions_from_file()
        
        self.logger.info("Frontend service initialized")

    async def create_session(self, category: str = "health_care", agent_name: str = None):
        """Create new session with dynamic template discovery"""
        
        # Store original values for error messages
        original_category = category
        original_agent_name = agent_name

        # Use agent name directly from URL
        if agent_name:
            agent_name = agent_name.lower()
        
        self.logger.info(f"Creating session - Category: {category}, Agent: {agent_name}")
        
        # Dynamic template discovery
        if agent_name:
            # Try to find template that matches category and agent name
            candidate_template = f"{category}_{agent_name}"
            if self.letta_service.get_template(candidate_template):
                template_name = candidate_template
            else:
                # Try to find any template in this category that contains the agent name
                category_templates = self.letta_service.find_templates_by_category(category)
                matching_templates = [t for t in category_templates if agent_name in t.lower() and not t.endswith(('_user', '_case'))]
                
                if matching_templates:
                    template_name = matching_templates[0]
                else:
                    # Use default template for category
                    template_name = self.letta_service.get_default_template_for_category(category)
        else:
            # Use default template for category
            template_name = self.letta_service.get_default_template_for_category(category)
        
        if not template_name:
            available_categories = set()
            all_templates = self.letta_service.get_available_templates()
            for t in all_templates:
                if '_' in t:
                    available_categories.add(t.split('_')[0])
            raise ValueError(
                f"No templates found for category '{category}' with agent '{agent_name or 'default'}'. "
                f"Available categories: {list(available_categories)}"
            )
        
        self.logger.info(f"Auto-discovered template: {template_name}")
        
        # Verify template exists
        if not self.letta_service.get_template(template_name):
            raise ValueError(f"Template {template_name} not found after auto-discovery")
        
        # Generate unique session ID with stronger collision detection
        import time
        import random
        
        max_attempts = 20
        session_id = None
        
        for attempt in range(max_attempts):
            # Use timestamp + random for better uniqueness
            base_id = uuid.uuid4().hex[:7]
            
            # Check both in-memory sessions and potential file conflicts
            if base_id not in self.session_data:
                # Additional check for folder existence to prevent filesystem conflicts
                session_folder = os.path.join(self.settings.FOLDERS_BASE_DIR, f"session_{base_id}")
                if not os.path.exists(session_folder):
                    session_id = base_id
                    break
                else:
                    self.logger.warning(f"Session folder collision detected: {base_id}")
            else:
                self.logger.warning(f"Session ID collision detected in memory: {base_id}")
            
            # If collision detected, add randomness
            if attempt >= 5:
                # Add more entropy after several attempts
                timestamp_part = f"{int(time.time() * 1000) % 1000:03d}"
                random_part = f"{random.randint(0, 999):03d}"
                session_id = f"{timestamp_part}{random_part:0<4}"[:7]
                
                if session_id not in self.session_data:
                    session_folder = os.path.join(self.settings.FOLDERS_BASE_DIR, f"session_{session_id}")
                    if not os.path.exists(session_folder):
                        break
        
        if not session_id:
            # Final fallback - use current timestamp with high precision
            session_id = f"{int(time.time() * 1000000) % 10000000:07x}"
        
        self.logger.info(f"Creating session: {session_id} (attempt {attempt + 1})")
        
        try:
            # Create session and continuia folders
            folder_paths = self._create_session_folders(session_id)
            
            # Create agent using LettaAPIService
            agent_result = await self.letta_service.create_agent_from_template(
                template_name=template_name,
                variables={
                    'session_id': session_id,
                    'phase': 'anonymous'
                }
            )
            
            if not agent_result["success"]:
                raise Exception(f"Failed to create agent: {agent_result['error']}")
            
            agent_id = agent_result["agent_id"]
            agent_name = agent_result["agent_name"]
            
            # Extract template metadata
            template_data = self.letta_service.get_template(template_name)
            actual_category = template_data['metadata']['category']
            actual_agent_type = template_data['metadata']['original_name']
            
            # Store session data
            self.session_data[session_id] = {
                "agent_id": agent_id,
                "agent_name": agent_name,
                "template_name": template_name,
                "category": actual_category,
                "agent_type": actual_agent_type,
                "phase": "anonymous",
                "case_id": None,
                "user_id": None,
                "folders": {
                    "session_folder": folder_paths["session_folder"],
                    "continuia_folder": folder_paths["continuia_folder"]
                },
                "created_at": datetime.utcnow().isoformat(),
                "welcome_sent": False,
                "original_request": {
                    "category": original_category,
                    "agent_name": original_agent_name
                }
            }
            
            # Save sessions to file
            self._save_sessions_to_file()
            
            # Session ID is available in session data - no need for API call
            
            self.logger.info(f"Created session {session_id} with agent {agent_name}")
            
            return {
                "sessionId": session_id,
                "agentId": agent_id,
                "category": actual_category,
                "agentType": actual_agent_type,
                "phase": "anonymous",
                "welcomeSent": False
            }
            
        except Exception as e:
            self.logger.error(f"Error in create_session: {str(e)}")
            # Clean up session data if it was partially created
            if session_id in self.session_data:
                del self.session_data[session_id]
            raise

    async def handle_websocket_with_handshake(self, websocket: WebSocket, session_id: str, agent_name: str, is_new_session: bool = False):
        """Handle WebSocket communication after initial handshake is already processed"""
        connection_info = f"Session: {session_id}, Agent: {agent_name}"
        
        try:
            self.logger.info(f"Starting WebSocket handler (handshake processed) - {connection_info}")

            if session_id not in self.session_data:
                self.logger.error(f"Session {session_id} not found")
                return
            
            # Get session information
            current_session = self.session_data[session_id]
            initial_agent_id = current_session["agent_id"]
            template_name = current_session["template_name"]
            category = current_session["category"]
            agent_type = current_session["agent_type"]
            
            self.logger.info(f"Session {session_id} using agent: {initial_agent_id}")
            
            # Send connection established message with welcome content
            welcome_sent = current_session.get("welcome_sent", False)
            
            if not welcome_sent or is_new_session:
                self.logger.info(f"Sending welcome messages for session {session_id}")
                
                welcome_content = self._get_welcome_message(category, agent_type, template_name)
                
                # Include new session indication if this is a newly created session
                message_text = "Connected - New Session Created" if is_new_session else "Connected"
                
                welcome_success = await self._safe_send_json(websocket, {
                    "type": "connection_established",
                    "session_id": session_id,
                    "agent_id": initial_agent_id,
                    "agent_name": agent_type,
                    "message": message_text,
                    "content": welcome_content,
                    "UX_Command": "session",
                    "new_session": is_new_session
                })

                if not welcome_success:
                    self.logger.warning(f"Failed to send welcome message, client may have disconnected - {connection_info}")
                    return

                # Get consent message from template
                consent_content = self._get_consent_message(template_name)
                if consent_content:
                    consent_success = await self._safe_send_json(websocket, {
                        "type": "agent_response",
                        "session_id": session_id,
                        "agent_id": initial_agent_id,
                        "agent_name": agent_type,
                        "content": consent_content,
                        "UX_Command": False,
                        "message_type": "disclaimer"
                    })
                    
                    if not consent_success:
                        self.logger.warning(f"Failed to send consent message, client may have disconnected - {connection_info}")
                        return
                
                # Mark welcome as sent
                self.session_data[session_id]["welcome_sent"] = True
                self.session_welcome_sent[session_id] = True
                self._save_sessions_to_file()
                self.logger.info(f"Welcome messages sent successfully - {connection_info}")
                     
            else:
                # Reconnection message - send welcome content for reconnections too
                current_agent_id = self.session_data[session_id]["agent_id"]
                welcome_content = self._get_welcome_message(category, agent_type, template_name)
                reconnect_success = await self._safe_send_json(websocket, {
                    "type": "connection_established",
                    "session_id": session_id,
                    "agent_id": current_agent_id,
                    "agent_name": agent_type,
                    "message": "Reconnected",
                    "content": welcome_content,
                    "UX_Command": "session"
                })
                
                if not reconnect_success:
                    self.logger.warning(f"Failed to send reconnection message, client may have disconnected - {connection_info}")
                    return
                    
                self.logger.info(f"Reconnection message sent successfully - {connection_info}")
            
            # Message handling loop
            while True:
                try:
                    data = await websocket.receive_json()
                    self.logger.debug(f"Received message from client - {connection_info}")
                    
                except WebSocketDisconnect as e:
                    self.logger.info(f"WebSocket client disconnected gracefully (code: {e.code}) - {connection_info}")
                    break
                    
                except Exception as receive_error:
                    # Handle specific WebSocket errors
                    error_msg = str(receive_error)
                    if "1001" in error_msg:
                        self.logger.info(f"WebSocket client going away (1001) - {connection_info}")
                    elif "1000" in error_msg:
                        self.logger.info(f"WebSocket normal closure (1000) - {connection_info}")
                    elif "ConnectionClosed" in error_msg:
                        self.logger.info(f"WebSocket connection closed by client - {connection_info}")
                    else:
                        self.logger.warning(f"WebSocket receive error: {error_msg} - {connection_info}")
                    break
                
                user_message = self._extract_user_message(data)
                if not user_message:
                    current_agent_id = self.session_data[session_id]["agent_id"]
                    error_success = await self._safe_send_json(websocket, {
                        "type": "agent_response",
                        "session_id": session_id,
                        "agent_id": current_agent_id,
                        "agent_name": agent_type,
                        "content": "I didn't receive your message properly. Could you please try again?",
                        "message_type": "error",
                        "UX_Command": False
                    })
                    
                    if not error_success:
                        self.logger.warning(f"Failed to send error message, breaking connection - {connection_info}")
                        break
                    continue
                
                # Get current agent_id from session
                current_agent_id = self.session_data[session_id]["agent_id"]
                
                # Send message to agent using LettaAPIService
                try:
                    self.logger.info(f"Sending message to agent {current_agent_id}: {user_message[:50]}...")
                    
                    message_result = self.letta_service.send_message_to_agent(current_agent_id, user_message)
                    
                    if not message_result["success"]:
                        raise Exception(f"Failed to send message: {message_result['error']}")
                    
                    agent_response = message_result["agent_response"]
                    
                    # Check if OTP was successfully verified
                    otp_result = await self._check_otp_success_and_update_ux(
                        agent_id=current_agent_id,
                        user_message=user_message,
                        agent_response=agent_response,
                        session_id=session_id
                    )
                    
                    # Set UX_Command based on OTP success
                    ux_command_value = "login" if otp_result.get("success", False) else False
                    
                    response_data = {
                        "type": "agent_response",
                        "session_id": session_id,
                        "agent_id": current_agent_id,
                        "agent_name": agent_type,
                        "content": agent_response,
                        "UX_Command": ux_command_value
                    }
                    
                    if otp_result.get("success"):
                        response_data["login"] = otp_result.get("login_data", {})
                    
                    response_success = await self._safe_send_json(websocket, response_data)
                    if not response_success:
                        self.logger.warning(f"Failed to send agent response, breaking connection - {connection_info}")
                        break
                    
                except Exception as e:
                    self.logger.error(f"Agent communication error: {e} - {connection_info}")
                    error_response_success = await self._safe_send_json(websocket, {
                        "type": "agent_response",
                        "session_id": session_id,
                        "agent_id": current_agent_id,
                        "agent_name": agent_type,
                        "content": "I apologize for the technical issue. Could you please try again?",
                        "UX_Command": False
                    })
                    
                    if not error_response_success:
                        self.logger.warning(f"Failed to send error response, breaking connection - {connection_info}")
                        break
                        
        except WebSocketDisconnect as e:
            self.logger.info(f"WebSocket disconnected during setup (code: {e.code}) - {connection_info}")
            
        except Exception as e:
            self.logger.error(f"WebSocket handler error: {e} - {connection_info}")
            
        finally:
            self.logger.info(f"WebSocket connection handler finished - {connection_info}")

    async def authenticate_session(self, session_id: str):
        """Transition session to authenticated phase"""
        if session_id not in self.session_data:
            raise ValueError("Session not found")
        
        current_session = self.session_data[session_id]
        original_template = current_session["template_name"]
        
        # Generate IDs
        case_id = uuid.uuid4().hex[:12]
        user_id = uuid.uuid4().hex[:12]
        
        # Extract data from current agent, including user info
        scratch_data = self._extract_scratch_data(current_session["agent_id"])
        
        # Extract user details from scratch data for the new agent
        user_name = "User"
        user_phone = "unknown"
        
        if scratch_data and scratch_data.get("content"):
            content = scratch_data["content"]
            lines = content.split('\n')
            for line in lines:
                line = line.strip()
                if any(pattern in line.lower() for pattern in ['user_name:', 'name:', 'full_name:']):
                    if ':' in line:
                        name_value = line.split(':', 1)[1].strip()
                        # Clean up any trailing semicolons and additional data
                        if ';' in name_value:
                            name_value = name_value.split(';')[0].strip()
                        if name_value and name_value.lower() not in ['null', 'none', '']:
                            user_name = name_value
                elif any(pattern in line.lower() for pattern in ['user_phone:', 'phone:', 'phone_number:']):
                    if ':' in line:
                        phone_value = line.split(':', 1)[1].strip()
                        # Extract only the phone number part before any semicolon
                        if ';' in phone_value:
                            phone_value = phone_value.split(';')[0].strip()
                        # Use regex to extract valid phone number format
                        if phone_value and phone_value.lower() not in ['null', 'none', '']:
                            import re
                            # Extract phone number pattern +91 followed by exactly 10 digits
                            phone_match = re.search(r'\+91\d{10}', phone_value)
                            if phone_match:
                                user_phone = phone_match.group()
                                self.logger.info(f"Extracted phone number: {user_phone}")
        
        self.logger.info(f"Extracted user data - Name: {user_name}, Phone: {user_phone}")
        
        # Create properly sanitized phone number for agent name (only digits)
        if user_phone != "unknown" and user_phone.startswith('+91') and len(user_phone) == 13:
            # Extract only digits after +91 (should be exactly 10 digits)
            user_phone_sanitized = user_phone[3:]  # Remove +91 prefix
            # Additional validation - ensure it's only digits
            if user_phone_sanitized.isdigit() and len(user_phone_sanitized) == 10:
                self.logger.info(f"Sanitized phone for agent naming: {user_phone_sanitized}")
            else:
                self.logger.warning(f"Phone sanitization failed: {user_phone} -> {user_phone_sanitized}")
                user_phone_sanitized = "unknown"
        else:
            self.logger.warning(f"Invalid phone format for sanitization: {user_phone}")
            user_phone_sanitized = "unknown"
        
        self.logger.info(f"Sanitized phone number for agent naming: {user_phone_sanitized}")
        
        # Determine the authenticated user template name
        # Switch from intake template to user template
        if "arika_reddy" in original_template.lower():
            # Switch to user agent template
            authenticated_template = "health_care_arika_reddy_user"
        else:
            # Fallback to original template with authenticated phase
            authenticated_template = original_template
        
        self.logger.info(f"Creating authenticated user agent using template: {authenticated_template}")
        
        # Create new authenticated user agent
        agent_result = await self.letta_service.create_agent_from_template(
            template_name=authenticated_template,
            variables={
                'session_id': session_id,
                'user_phone': user_phone,
                'user_phone_sanitized': user_phone_sanitized,
                'user_name': user_name,
                'case_id': case_id,
                'user_id': user_id
            }
        )
        
        if not agent_result["success"]:
            raise Exception(f"Failed to create authenticated agent: {agent_result['error']}")
        
        new_agent_id = agent_result["agent_id"]
        new_agent_name = agent_result["agent_name"]
        
        # Transfer scratch data to new authenticated agent
        if scratch_data and scratch_data.get("content"):
            try:
                self.logger.info(f"Transferring user data to new authenticated agent {new_agent_id}")
                
                # Create a comprehensive transfer message with all collected user data
                transfer_message = f"Please save this user information and conversation history from the intake process:\n\n{scratch_data['content']}\n\nThis contains all the user data collected during the anonymous intake phase. Please integrate this information into your user profile and continue the healthcare coordination from where the intake left off."
                
                # Send the transfer message to the new agent
                transfer_result = self.letta_service.send_message_to_agent(new_agent_id, transfer_message)
                
                if transfer_result["success"]:
                    self.logger.info(f"Successfully transferred user data to authenticated agent {new_agent_id}")
                else:
                    self.logger.error(f"Failed to transfer data to authenticated agent: {transfer_result.get('error')}")
                    
            except Exception as transfer_error:
                self.logger.error(f"Error during data transfer to authenticated agent: {transfer_error}")
        
        # Update session (preserve welcome_sent status)
        welcome_sent_status = current_session.get("welcome_sent", False)
        self.session_data[session_id].update({
            "agent_id": new_agent_id,
            "agent_name": new_agent_name,
            "phase": "authenticated",
            "case_id": case_id,
            "user_id": user_id,
            "previous_agent_id": current_session["agent_id"],
            "authenticated_at": datetime.utcnow().isoformat(),
            "welcome_sent": welcome_sent_status
        })
        
        # Save sessions to file
        self._save_sessions_to_file()
        
        self.logger.info(f"Session {session_id} authenticated - Case: {case_id}, User: {user_id}")
        
        return {
            "sessionId": session_id,
            "agentId": new_agent_id,
            "caseId": case_id,
            "userId": user_id
        }

    async def _safe_send_json(self, websocket: WebSocket, data: dict) -> bool:
        """Safely send JSON data over WebSocket with connection state checking"""
        try:
            # Check if WebSocket is still connected
            if websocket.client_state == WebSocketState.DISCONNECTED:
                self.logger.debug("WebSocket already disconnected, skipping send")
                return False
            
            await websocket.send_json(data)
            return True
            
        except WebSocketDisconnect as e:
            self.logger.debug(f"WebSocket disconnected during send (code: {e.code})")
            return False
            
        except Exception as e:
            error_msg = str(e)
            if "1001" in error_msg:
                self.logger.debug("WebSocket client going away during send")
            elif "1000" in error_msg:
                self.logger.debug("WebSocket normal closure during send")
            elif "ConnectionClosed" in error_msg:
                self.logger.debug("WebSocket connection closed during send")
            else:
                self.logger.warning(f"WebSocket send error: {error_msg}")
            return False

    async def _check_otp_success_and_update_ux(self, agent_id: str, user_message: str, agent_response: str, session_id: str = None) -> dict:
        """Check if OTP verification was successful and handle post-OTP logic"""
        try:
            # Check if user message contains an OTP (6 digits)
            otp_match = re.search(r'\b\d{6}\b', user_message.strip())
            if not otp_match:
                return {"success": False}
            
            # Check agent response for success indicators
            success_indicators = [
                "verification is complete",
                "verification complete",
                "verified successfully",
                "phone number has been verified",
                "perfect! your verification is complete"
            ]
            
            agent_response_lower = agent_response.lower()
            otp_success = any(indicator in agent_response_lower for indicator in success_indicators)
            
            if otp_success:
                self.logger.info(f"OTP success detected for agent {agent_id}")
                
                # Extract user data from agent response
                user_name = None
                user_phone = None
                
                response_lines = agent_response.split('\n')
                for line in response_lines:
                    if '• Name:' in line and not user_name:
                        name_part = line.split('• Name:')[1]
                        user_name = name_part.split('\n')[0].split(',')[0].strip()
                    elif '• Phone:' in line and not user_phone:
                        phone_match = re.search(r'\+91\d{10}', line)
                        if phone_match:
                            user_phone = phone_match.group()
                
                # If not found in response, try memory blocks
                if not user_name or not user_phone:
                    memory_result = self.letta_service.get_agent_memory_block(agent_id, "scratch")
                    if memory_result["success"] and memory_result["block"]:
                        scratch_content = memory_result["block"].value
                        lines = scratch_content.split('\n')
                        for line in lines:
                            line = line.strip()
                            if any(pattern in line.lower() for pattern in ['user_name:', 'name:', 'full_name:']):
                                if ':' in line:
                                    name_value = line.split(':', 1)[1].strip()
                                    if name_value and name_value.lower() not in ['null', 'none', '']:
                                        user_name = name_value
                            elif any(pattern in line.lower() for pattern in ['user_phone:', 'phone:', 'phone_number:']):
                                if ':' in line:
                                    phone_value = line.split(':', 1)[1].strip()
                                    # Extract only the phone number part before any semicolon
                                    if ';' in phone_value:
                                        phone_value = phone_value.split(';')[0].strip()
                                    # Use regex to extract valid phone number format
                                    if phone_value and phone_value.lower() not in ['null', 'none', '']:
                                        import re
                                        phone_match = re.search(r'\+91\d{10}', phone_value)
                                        if phone_match:
                                            user_phone = phone_match.group()
                
                # Create login data object (only phone and name)
                login_data = {
                    'phone': user_phone if user_phone else 'unknown',
                    'name': user_name if user_name else 'User'
                }
                
                # CRITICAL: Trigger authentication transition after successful OTP
                if session_id:
                    self.logger.info(f"Triggering authentication transition for session {session_id}")
                    try:
                        # Call authenticate_session to create new authenticated agent
                        auth_result = await self.authenticate_session(session_id)
                        if auth_result:
                            self.logger.info(f"Successfully created authenticated agent for session {session_id}")
                        else:
                            self.logger.error(f"Failed to create authenticated agent for session {session_id}")
                    except Exception as auth_error:
                        self.logger.error(f"Error during authentication transition: {auth_error}")
                
                return {
                    "success": True,
                    "login_data": login_data
                }
                
            return {"success": False}
            
        except Exception as e:
            self.logger.error(f"Error checking OTP success: {e}")
            return {"success": False}

    def _extract_scratch_data(self, agent_id: str) -> Dict[str, Any]:
        """Extract scratch data from agent"""
        try:
            memory_result = self.letta_service.get_agent_memory_block(agent_id, "scratch")
            if memory_result["success"] and memory_result["block"]:
                return {"extracted": "data", "content": memory_result["block"].value}
            return {}
        except Exception as e:
            self.logger.error(f"Failed to extract scratch data: {e}")
            return {}

    def _get_welcome_message(self, category: str, agent_type: str, template_name: str = None) -> str:
        """Get template-specific welcome message - only from YAML templates"""
        
        # Only get welcome message from template YAML files
        if template_name:
            template_data = self.letta_service.get_template(template_name)
            if template_data:
                agent_template = template_data.get('agent_template', {})
                required_messages = agent_template.get('required_messages', {})
                welcome_msg = required_messages.get('welcome_message')
                if welcome_msg:
                    return welcome_msg
                
                # Fallback to old welcome_message field
                template_welcome = agent_template.get('welcome_message')
                if template_welcome:
                    return template_welcome
        
        # If no template welcome message found, return generic message
        # All welcome messages should be defined in YAML templates
        self.logger.warning(f"No welcome message found in template {template_name}. Please add welcome_message to the template YAML file.")
        return f"Hello! I'm here to assist you. How can I help you today?"

    def _get_consent_message(self, template_name: str) -> str:
        """Get consent message from template"""
        if not template_name:
            return ""
        
        template_data = self.letta_service.get_template(template_name)
        if template_data:
            agent_template = template_data.get('agent_template', {})
            required_messages = agent_template.get('required_messages', {})
            return required_messages.get('consent_message', '')
        
        return ""

    def _is_system_message(self, content: str) -> bool:
        """Check if a message is a system message that should be hidden from users"""
        try:
            # Try to parse as JSON for structured system messages
            parsed = json.loads(content)
            if isinstance(parsed, dict):
                msg_type = parsed.get('type', '')
                if msg_type in ['login', 'heartbeat', 'session_id']:
                    return True
        except (json.JSONDecodeError, TypeError):
            pass
        
        # Check for other system message patterns
        system_patterns = [
            'Session ID:',
            'heartbeat',
            'automated system message',
            'hidden from the user',
            'Please save this user information and conversation history',
            'session initialized - data will be added',
            'This contains all the user data collected',
            'More human than human is our motto'
        ]
        
        content_lower = content.lower()
        return any(pattern.lower() in content_lower for pattern in system_patterns)

    def _clean_agent_message_content(self, content: str) -> str:
        """Clean up malformed agent message content"""
        if not content:
            return content
        
        try:
            # Remove malformed JSON patterns
            if "','request_heartbeat'" in content:
                cut_position = content.find("','request_heartbeat'")
                if cut_position > 0:
                    content = content[:cut_position]
            
            return content.rstrip()
            
        except Exception as e:
            self.logger.error(f"Error during content cleaning: {e}")
            return content

    def _extract_user_message(self, data: Dict) -> str:
        """Extract user message from different possible keys"""
        possible_keys = ["message", "content", "text", "msg", "user_message"]
        for key in possible_keys:
            if key in data and data[key]:
                return data[key]
        return ""

    def _create_session_folders(self, session_id: str) -> Dict[str, str]:
        """Create and return folder paths for session"""
        base_folder_path = self.settings.FOLDERS_BASE_DIR
        
        # Create session-specific folder
        session_folder_path = os.path.join(base_folder_path, f"session_{session_id}")
        
        # Continuia folder path
        continuia_folder_path = os.path.join(base_folder_path, "continuia")
        
        try:
            os.makedirs(session_folder_path, exist_ok=True)
            os.makedirs(continuia_folder_path, exist_ok=True)
            
            return {
                "session_folder": session_folder_path,
                "continuia_folder": continuia_folder_path
            }
        except Exception as e:
            self.logger.error(f"Failed to create folders: {e}")
            # Return fallback paths
            return {
                "session_folder": "/tmp",
                "continuia_folder": "/tmp"
            }

    def _save_sessions_to_file(self):
        """Save all sessions to JSON file"""
        try:
            serializable_data = {
                "sessions": {}
            }
            
            for session_id, data in self.session_data.items():
                serializable_data["sessions"][session_id] = {
                    "agent_id": data["agent_id"],
                    "agent_name": data["agent_name"],
                    "template_name": data["template_name"],
                    "category": data["category"],
                    "agent_type": data["agent_type"],
                    "phase": data["phase"],
                    "case_id": data.get("case_id"),
                    "user_id": data.get("user_id"),
                    "folders": data.get("folders", {}),
                    "created_at": data.get("created_at", datetime.utcnow().isoformat()),
                    "last_accessed": datetime.utcnow().isoformat(),
                    "welcome_sent": data.get("welcome_sent", False)
                }
            
            with open(self.sessions_file, 'w') as f:
                json.dump(serializable_data, f, indent=2, default=str)
            
            self.logger.info(f"Saved {len(serializable_data['sessions'])} sessions to {self.sessions_file}")
            
        except Exception as e:
            self.logger.error(f"Failed to save sessions to file: {e}")

    def _load_sessions_from_file(self):
        """Load sessions from JSON file on startup"""
        try:
            if os.path.exists(self.sessions_file):
                with open(self.sessions_file, 'r') as f:
                    saved_data = json.load(f)
                
                # Handle both old and new file formats
                if "sessions" in saved_data:
                    self.session_data = saved_data["sessions"]
                else:
                    self.session_data = saved_data
                
                # Load welcome status
                for session_id, data in self.session_data.items():
                    if data.get("welcome_sent", False):
                        self.session_welcome_sent[session_id] = True
                
                self.logger.info(f"Loaded {len(self.session_data)} sessions from {self.sessions_file}")
                
            else:
                self.logger.info(f"No existing sessions file found")
                self.session_data = {}
                
        except Exception as e:
            self.logger.error(f"Failed to load sessions from file: {e}")
            self.session_data = {}

    def get_session_data(self, session_id: str) -> dict:
        """Get session data for a session"""
        return self.session_data.get(session_id)

    def get_agent_id_for_session(self, session_id: str) -> str:
        """Get agent ID for a session"""
        if session_id in self.session_data:
            return self.session_data[session_id]["agent_id"]
        return None

    def get_agent_messages(self, session_id: str, include_welcome: bool = True):
        """Get messages for a session, handling multiple agents if needed"""
        try:
            self.logger.info(f"Getting messages for session: {session_id}")
            
            if session_id not in self.session_data:
                self.logger.error(f"Session {session_id} not found")
                return []
            
            session_data = self.session_data[session_id]
            current_agent_id = session_data.get("agent_id")
            
            messages = []
            all_letta_messages = []
            
            # Get messages from previous agents if they exist
            previous_agents = []
            if session_data.get("previous_agent_id"):
                previous_agents.append(session_data["previous_agent_id"])
            if session_data.get("previous_intake_agent_id"):
                previous_agents.append(session_data["previous_intake_agent_id"])
            
            for prev_agent_id in previous_agents:
                prev_result = self.letta_service.get_agent_messages(prev_agent_id, limit=1000)
                if prev_result["success"]:
                    # Skip first few messages from previous agents (system messages)
                    prev_messages = prev_result["messages"][4:] if len(prev_result["messages"]) > 4 else []
                    all_letta_messages.extend(prev_messages)
                    self.logger.info(f"Added {len(prev_messages)} messages from previous agent {prev_agent_id}")
            
            # Get messages from current agent
            if current_agent_id:
                current_result = self.letta_service.get_agent_messages(current_agent_id, limit=1000)
                if current_result["success"]:
                    # Skip first few messages from current agent (system messages)
                    # Skip more messages to avoid initial agent prompts appearing before user input
                    skip_count = max(4, len(current_result["messages"]) - 20) if len(current_result["messages"]) > 20 else 4
                    current_messages = current_result["messages"][skip_count:] if len(current_result["messages"]) > skip_count else []
                    all_letta_messages.extend(current_messages)
                    self.logger.info(f"Added {len(current_messages)} messages from current agent (skipped first {skip_count})")
            
            # Sort messages chronologically with proper datetime handling
            def get_message_timestamp(msg):
                date_value = getattr(msg, "date", None)
                if not date_value:
                    return datetime.min
                
                # Handle both datetime objects and string timestamps
                if isinstance(date_value, datetime):
                    return date_value
                elif isinstance(date_value, str):
                    try:
                        # Simple ISO format parsing without external dependencies
                        import re
                        # Remove timezone info for consistent sorting
                        clean_date = re.sub(r'[+\-]\d{2}:\d{2}$|Z$', '', date_value)
                        return datetime.fromisoformat(clean_date)
                    except Exception as parse_error:
                        self.logger.warning(f"Failed to parse timestamp string {date_value}: {parse_error}")
                        return datetime.min
                else:
                    return datetime.min
            
            # First pass: find actual user messages (not Session ID)
            user_messages_exist = False
            for msg in all_letta_messages:
                if hasattr(msg, 'message_type') and msg.message_type == 'user_message':
                    content = getattr(msg, 'content', '')
                    if not content.startswith('Session ID:') and content.strip():
                        user_messages_exist = True
                        break
            
            # If no user messages exist yet, skip ALL agent messages from Letta
            # Only show welcome and consent (these will be added later)
            if not user_messages_exist:
                self.logger.info(f"No user messages found - showing only welcome/consent for new session")
                all_letta_messages = []  # Skip all Letta messages for new sessions
            else:
                # For sessions with user interaction, filter properly
                first_user_timestamp = None
                for msg in all_letta_messages:
                    if hasattr(msg, 'message_type') and msg.message_type == 'user_message':
                        content = getattr(msg, 'content', '')
                        if not content.startswith('Session ID:') and content.strip():
                            first_user_timestamp = get_message_timestamp(msg)
                            break
                
                if first_user_timestamp:
                    filtered_messages = []
                    for msg in all_letta_messages:
                        msg_timestamp = get_message_timestamp(msg)
                        
                        # Always include user messages
                        if hasattr(msg, 'message_type') and msg.message_type == 'user_message':
                            filtered_messages.append(msg)
                        # Only include agent messages that come after first user interaction
                        elif hasattr(msg, 'message_type') and msg.message_type == 'assistant_message':
                            if msg_timestamp >= first_user_timestamp:
                                filtered_messages.append(msg)
                            else:
                                self.logger.debug(f"Filtered out early agent message: {getattr(msg, 'content', '')[:50]}")
                    
                    all_letta_messages = filtered_messages
                    self.logger.info(f"Filtered messages to start from first user interaction at {first_user_timestamp}")
                
                # Sort chronologically
                all_letta_messages.sort(key=get_message_timestamp)
            
            # Process messages for frontend
            conversation_messages = []
            
            for msg in all_letta_messages:
                if not hasattr(msg, 'message_type'):
                    continue
                    
                # Skip system messages
                if msg.message_type in ['system_message', 'reasoning_message', 'tool_call_message', 'tool_return_message']:
                    continue
                
                content = getattr(msg, 'content', '')
                
                # Process user messages
                if msg.message_type == 'user_message':
                    # Skip Session ID messages
                    if content.startswith('Session ID:'):
                        continue
                    if not self._is_system_message(content):
                        conversation_messages.append({
                            "id": f"message-{getattr(msg, 'id', 'unknown')}",
                            "role": "user",
                            "content": content,
                            "timestamp": getattr(msg, 'date', datetime.now().isoformat())
                        })
                
                # Process assistant messages
                elif msg.message_type == 'assistant_message':
                    # Skip motto message
                    if content.strip() == 'More human than human is our motto.':
                        continue
                    
                    cleaned_content = self._clean_agent_message_content(content)
                    if cleaned_content.strip():
                        conversation_messages.append({
                            "id": f"message-{getattr(msg, 'id', 'unknown')}",
                            "role": "agent",
                            "content": cleaned_content,
                            "timestamp": getattr(msg, 'date', datetime.now().isoformat())
                        })
            
            # Remove duplicates first
            seen_ids = set()
            deduplicated_messages = []
            for msg in conversation_messages:
                msg_id = msg.get('id', 'no-id')
                if msg_id not in seen_ids:
                    seen_ids.add(msg_id)
                    deduplicated_messages.append(msg)
            
            # Sort conversation messages chronologically by timestamp
            deduplicated_messages.sort(key=lambda msg: msg.get('timestamp', ''))
            
            # Add welcome and consent messages at the beginning if requested AND welcome was sent before
            if include_welcome and session_data.get("welcome_sent", False):
                category = session_data.get('category', 'default')
                agent_type = session_data.get('agent_type', 'default')
                template_name = session_data.get('template_name')
                
                welcome_content = self._get_welcome_message(category, agent_type, template_name)
                
                welcome_message = {
                    "id": f"welcome-{session_id}",
                    "role": "agent",
                    "content": welcome_content,
                    "timestamp": datetime.now().isoformat()
                }
                messages.append(welcome_message)
                
                # Get consent message from template
                consent_content = self._get_consent_message(template_name)
                if consent_content:
                    consent_message = {
                        "id": f"consent-{session_id}",
                        "role": "agent",
                        "content": consent_content,
                        "timestamp": datetime.now().isoformat(),
                        "message_type": "disclaimer"
                    }
                    messages.append(consent_message)
            
            # Add conversation messages in chronological order
            messages.extend(deduplicated_messages)
            
            self.logger.info(f"Returning {len(messages)} total messages for session {session_id}")
            return messages
            
        except Exception as e:
            self.logger.error(f"Error getting agent messages: {e}")
            return []

    def find_existing_user_agent_by_phone(self, phone_number: str):
        """Find existing user agent by phone number"""
        try:
            result = self.letta_service.find_user_agent_by_phone(phone_number)
            if result["success"]:
                agent = result["agent"]
                self.logger.info(f"Found existing user agent {agent.id} for phone {phone_number}")
                return {
                    "success": True,
                    "agent_id": agent.id,
                    "agent_name": agent.name,
                    "agent": agent
                }
            else:
                self.logger.info(f"No existing user agent found for phone {phone_number}")
                return {"success": False, "error": result["error"]}
                
        except Exception as e:
            self.logger.error(f"Error finding user agent by phone {phone_number}: {e}")
            return {"success": False, "error": str(e)}