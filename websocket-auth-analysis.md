# WebSocket Authentication Analysis

## 🔍 **Current Situation**

### **Why WebSocketAuthMiddleware Exists Separately**

The `WebSocketAuthMiddleware` exists as a separate implementation from `opalAuthMiddleware` due to fundamental differences between HTTP and WebSocket protocols:

#### **1. Protocol Differences**
- **HTTP Routes**: Use Express middleware pattern `(req, res, next)`
- **WebSocket**: Uses `ws` library's `verifyClient` callback pattern

#### **2. Parameter Extraction**
- **HTTP**: Parameters from URL path, query string, request body
- **WebSocket**: Parameters from custom headers (`x-case-id`, `x-note-type`, `x-user-id`)

#### **3. Connection Lifecycle**
- **HTTP**: Request-response cycle (stateless)
- **WebSocket**: Long-lived persistent connection (stateful)

## 🚨 **Problems with Current Implementation**

### **Code Duplication**
```typescript
// DUPLICATED in both opalAuth and websocketAuth
const decoded = jwt.verify(token, process.env.JWT_SECRET) as JWTPayload;

// DUPLICATED database user lookup
const [user] = await db
  .select({
    id: users.id,
    email: users.email,
    // ... same fields
  })
  .from(users)
  .where(eq(users.id, decoded.userId))
```

### **Missing Features**
- ❌ **No Policy Engine**: WebSocket auth was missing OPAL policy evaluation
- ❌ **Inconsistent Logging**: Different security event logging patterns
- ❌ **No Session Management**: Missing session tracking from opalAuth

## ✅ **Recent Improvements Made**

### **1. Unified Security Logging**
```typescript
// BEFORE: Basic logging
logger.warn('User not found for WebSocket connection', { userId });

// AFTER: Consistent security event logging
securityEventLogger.logFailedAuth(
  { headers: {}, ip: 'websocket', originalUrl: '/ws' } as any,
  'USER_NOT_FOUND',
  { userId: decoded.userId }
);
```

### **2. Added Policy Engine Integration**
```typescript
// NEW: Policy evaluation for WebSocket connections
const policyContext: PolicyContext = {
  user: {
    id: userId,
    email: user.email,
    firstName: user.firstName,
    lastName: user.lastName,
    roles: decoded.roles || ['patient'],
  },
  resource: {
    type: 'case_notes',
    id: params.caseId,
    attributes: { noteType: params.noteType },
  },
  action: 'collaborate', // WebSocket-specific action
  environment: {
    time: new Date(),
    ip: 'websocket',
    userAgent: 'websocket-client',
  },
};

const policyEngine = PolicyEngine.getInstance();
const policyResult = await policyEngine.evaluate(policyContext);
```

### **3. Consistent Error Handling**
```typescript
// JWT token validation now matches opalAuth patterns
if (error instanceof jwt.JsonWebTokenError) {
  securityEventLogger.logFailedAuth(
    { headers: {}, ip: 'websocket', originalUrl: '/ws' } as any,
    'INVALID_TOKEN',
    { error: error.message }
  );
} else if (error instanceof jwt.TokenExpiredError) {
  securityEventLogger.logFailedAuth(
    { headers: {}, ip: 'websocket', originalUrl: '/ws' } as any,
    'EXPIRED_TOKEN'
  );
}
```

## 🎯 **Why We Can't Fully Unify**

### **Technical Constraints**

1. **Different Middleware Patterns**:
   ```typescript
   // Express HTTP middleware
   export const opalAuthMiddleware = (req: Request, res: Response, next: NextFunction) => {
     // HTTP-specific logic
   };

   // WebSocket verification callback
   export class WebSocketAuthMiddleware {
     static async verifyClient(info: WebSocketVerificationInfo): Promise<boolean> {
       // WebSocket-specific logic
     }
   }
   ```

2. **Different Parameter Sources**:
   ```typescript
   // HTTP: URL parameters, query strings, body
   const caseId = req.params.id;
   const noteType = req.body.noteType;

   // WebSocket: Custom headers
   const caseId = req.headers['x-case-id'];
   const noteType = req.headers['x-note-type'];
   ```

3. **Different Response Patterns**:
   ```typescript
   // HTTP: Send response and end
   res.status(401).json({ error: 'Unauthorized' });

   // WebSocket: Return boolean for connection acceptance
   return false; // Reject connection
   ```

## 🚀 **Recommended Architecture**

### **Shared Core Logic**
Create a shared authentication service that both middleware can use:

```typescript
// shared-auth-service.ts
export class SharedAuthService {
  static async verifyJWT(token: string): Promise<AuthResult | null> {
    // Shared JWT verification logic
  }

  static async getUserFromDB(userId: string): Promise<User | null> {
    // Shared user lookup logic
  }

  static async evaluatePolicy(context: PolicyContext): Promise<PolicyResult> {
    // Shared policy evaluation logic
  }
}

// opalAuth.ts
export const opalAuthMiddleware = async (req, res, next) => {
  const authResult = await SharedAuthService.verifyJWT(token);
  const policyResult = await SharedAuthService.evaluatePolicy(context);
  // HTTP-specific handling
};

// websocketAuth.ts
export class WebSocketAuthMiddleware {
  static async verifyClient(info): Promise<boolean> {
    const authResult = await SharedAuthService.verifyJWT(token);
    const policyResult = await SharedAuthService.evaluatePolicy(context);
    // WebSocket-specific handling
  }
}
```

## 📊 **Current Status**

| Feature | opalAuthMiddleware | WebSocketAuthMiddleware | Status |
|---------|-------------------|------------------------|---------|
| JWT Verification | ✅ | ✅ | **Unified** |
| User Database Lookup | ✅ | ✅ | **Unified** |
| Policy Engine | ✅ | ✅ | **Added** |
| Security Event Logging | ✅ | ✅ | **Added** |
| Session Management | ✅ | ❌ | **Missing** |
| Token Blacklisting | ✅ | ❌ | **Missing** |

## 🔄 **Next Steps**

1. **Extract Shared Service**: Create `SharedAuthService` for common logic
2. **Add Session Management**: Integrate session tracking for WebSocket connections
3. **Add Token Blacklisting**: Support revoked tokens in WebSocket auth
4. **Unified Testing**: Create shared test utilities for both auth types
5. **Documentation**: Update API docs to explain both auth patterns

## 💡 **Conclusion**

The `WebSocketAuthMiddleware` **needs to exist separately** due to fundamental protocol differences, but we've successfully **unified the core security logic** by:

- ✅ Adding OPAL policy engine integration
- ✅ Implementing consistent security event logging  
- ✅ Standardizing JWT verification patterns
- ✅ Aligning error handling approaches

The current implementation now provides **equivalent security** for both HTTP and WebSocket connections while respecting the unique requirements of each protocol.
